<!-- Regular Field (non-grouped, non-multi) - EXACT from main component lines 54-198 - FormGroup Added -->
<div class="form-field" [formGroup]="form">
  <label [for]="field.fieldName">{{ field.label || field.fieldName }} @if (field.mandatory) {<span>*</span>}
@if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
 </label>

    @if (field.foreginKey) {
         <app-dropdown
          [fieldName]="field.fieldName"
          [formControl]="getFormControl(field.fieldName)"
          [config]="getRegularDropdownConfig(field)"
          [isDisabled]="isViewMode || field.noInput"
          [isReadonly]="isViewMode || field.noInput"
          [fields]="fields"
          [inputId]="getUniqueFieldId(field.fieldName)"
          (valueChange)="onDropdownValueChange($event)">
        </app-dropdown>
    } @else if (field.lookUp) {
         <app-dropdown
          [fieldName]="field.fieldName"
          [formControl]="getFormControl(field.fieldName)"
          [config]="getLookupDropdownConfig(field)"
          [isDisabled]="isViewMode || field.noInput"
          [isReadonly]="isViewMode || field.noInput"
          [fields]="fields"
          [inputId]="getUniqueFieldId(field.fieldName)"
          [options]="field.lookupOptions"
          (valueChange)="onDropdownValueChange($event)">
        </app-dropdown>
    } @else {
      <!-- Regular input fields for non-foreign key fields -->
      @if (field.type === 'boolean') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="checkbox" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
      }
      @if (field.type === 'string') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="text" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" [placeholder]="(field.label?.trim() || field.fieldName)" />
      }
      @if (field.type === 'int') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="number" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
      }
      @if (field.type === 'date') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="date" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
      }
      @if (field.type === 'double') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="number" step="00.50" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
      }
    }
  </div>
